{"text": "ცისარტყელა — ატმოსფერული ოპტიკური და მეტეოროლოგიური მოვლენა, რომელიც ხშირად წვიმის შემდეგ ჩნდება.", "phonemes": ["t", "s", "ʰ", "ˈ", "i", "s", "a", "r", "t", "q", "ˌ", "e", "l", "a", ",", " ", "ˈ", "a", "t", "m", "o", "s", "p", "ʰ", "ˌ", "e", "r", "u", "l", "i", " ", "ˈ", "o", "p", "t", "i", "k", "ˌ", "u", "r", "i", " ", "d", "a", " ", "m", "ˈ", "e", "t", "e", "ˌ", "o", "r", "o", "l", "ˌ", "o", "ɡ", "i", "ˌ", "u", "r", "i", " ", "m", "ˈ", "o", "v", "l", "e", "n", "a", ".", " ", "r", "ˈ", "o", "m", "e", "l", "i", "t", "s", "ʰ", " ", "x", "ʃ", "ˈ", "i", "r", "a", "d", " ", "t", "s", "v", "ˈ", "i", "m", "i", "s", " ", "ʃ", "ˈ", "e", "m", "d", "e", "ɡ", " ", "t", "ʃ", "n", "d", "ˈ", "e", "b", "a"], "phoneme_ids": [1, 0, 32, 0, 31, 0, 145, 0, 120, 0, 21, 0, 31, 0, 14, 0, 30, 0, 32, 0, 29, 0, 121, 0, 18, 0, 24, 0, 14, 0, 8, 0, 3, 0, 120, 0, 14, 0, 32, 0, 25, 0, 27, 0, 31, 0, 28, 0, 145, 0, 121, 0, 18, 0, 30, 0, 33, 0, 24, 0, 21, 0, 3, 0, 120, 0, 27, 0, 28, 0, 32, 0, 21, 0, 23, 0, 121, 0, 33, 0, 30, 0, 21, 0, 3, 0, 17, 0, 14, 0, 3, 0, 25, 0, 120, 0, 18, 0, 32, 0, 18, 0, 121, 0, 27, 0, 30, 0, 27, 0, 24, 0, 121, 0, 27, 0, 66, 0, 21, 0, 121, 0, 33, 0, 30, 0, 21, 0, 3, 0, 25, 0, 120, 0, 27, 0, 34, 0, 24, 0, 18, 0, 26, 0, 14, 0, 10, 0, 3, 0, 30, 0, 120, 0, 27, 0, 25, 0, 18, 0, 24, 0, 21, 0, 32, 0, 31, 0, 145, 0, 3, 0, 36, 0, 96, 0, 120, 0, 21, 0, 30, 0, 14, 0, 17, 0, 3, 0, 32, 0, 31, 0, 34, 0, 120, 0, 21, 0, 25, 0, 21, 0, 31, 0, 3, 0, 96, 0, 120, 0, 18, 0, 25, 0, 17, 0, 18, 0, 66, 0, 3, 0, 32, 0, 96, 0, 26, 0, 17, 0, 120, 0, 18, 0, 15, 0, 14, 0, 2]}
{"text": "ეს თავისებური რკალია ან წრეხაზი, რომელიც ფერების სპექტრისგან შედგება.", "phonemes": ["ˈ", "e", "s", " ", "t", "ʰ", "ˈ", "a", "v", "i", "s", "ˌ", "e", "b", "u", "r", "i", " ", "r", "k", "ˈ", "a", "l", "i", "a", " ", "ˈ", "a", "n", " ", "t", "s", "r", "ˈ", "e", "x", "a", "z", "i", ",", " ", "r", "ˈ", "o", "m", "e", "l", "i", "t", "s", "ʰ", " ", "p", "ʰ", "ˈ", "e", "r", "e", "b", "i", "s", " ", "s", "p", "ˈ", "e", "k", "ʰ", "t", "r", "i", "s", "ɡ", "a", "n", " ", "ʃ", "ˈ", "e", "d", "ɡ", "e", "b", "a", "."], "phoneme_ids": [1, 0, 120, 0, 18, 0, 31, 0, 3, 0, 32, 0, 145, 0, 120, 0, 14, 0, 34, 0, 21, 0, 31, 0, 121, 0, 18, 0, 15, 0, 33, 0, 30, 0, 21, 0, 3, 0, 30, 0, 23, 0, 120, 0, 14, 0, 24, 0, 21, 0, 14, 0, 3, 0, 120, 0, 14, 0, 26, 0, 3, 0, 32, 0, 31, 0, 30, 0, 120, 0, 18, 0, 36, 0, 14, 0, 38, 0, 21, 0, 8, 0, 3, 0, 30, 0, 120, 0, 27, 0, 25, 0, 18, 0, 24, 0, 21, 0, 32, 0, 31, 0, 145, 0, 3, 0, 28, 0, 145, 0, 120, 0, 18, 0, 30, 0, 18, 0, 15, 0, 21, 0, 31, 0, 3, 0, 31, 0, 28, 0, 120, 0, 18, 0, 23, 0, 145, 0, 32, 0, 30, 0, 21, 0, 31, 0, 66, 0, 14, 0, 26, 0, 3, 0, 96, 0, 120, 0, 18, 0, 17, 0, 66, 0, 18, 0, 15, 0, 14, 0, 10, 0, 2]}
{"text": "ცისარტყელა შედგება შვიდი ფერისგან: წითელი, ნარინჯისფერი, ყვითელი, მწვანე, ცისფერი, ლურჯი, იისფერი.", "phonemes": ["t", "s", "ʰ", "ˈ", "i", "s", "a", "r", "t", "q", "ˌ", "e", "l", "a", " ", "ʃ", "ˈ", "e", "d", "ɡ", "e", "b", "a", " ", "ʃ", "v", "ˈ", "i", "d", "i", " ", "p", "ʰ", "ˈ", "e", "r", "i", "s", "ɡ", "a", "n", ":", " ", "t", "s", "ˈ", "i", "t", "ʰ", "e", "l", "i", ",", " ", "n", "ˈ", "a", "r", "i", "n", "d", "ʒ", "ˌ", "i", "s", "p", "ʰ", "e", "r", "i", ",", " ", "q", "v", "ˈ", "i", "t", "ʰ", "e", "l", "i", ",", " ", "m", "t", "s", "v", "ˈ", "a", "n", "e", ",", " ", "t", "s", "ʰ", "ˈ", "i", "s", "p", "ʰ", "e", "r", "i", ",", " ", "l", "ˈ", "u", "r", "d", "ʒ", "i", ",", " ", "ˈ", "i", "i", "s", "p", "ʰ", "ˌ", "e", "r", "i", "."], "phoneme_ids": [1, 0, 32, 0, 31, 0, 145, 0, 120, 0, 21, 0, 31, 0, 14, 0, 30, 0, 32, 0, 29, 0, 121, 0, 18, 0, 24, 0, 14, 0, 3, 0, 96, 0, 120, 0, 18, 0, 17, 0, 66, 0, 18, 0, 15, 0, 14, 0, 3, 0, 96, 0, 34, 0, 120, 0, 21, 0, 17, 0, 21, 0, 3, 0, 28, 0, 145, 0, 120, 0, 18, 0, 30, 0, 21, 0, 31, 0, 66, 0, 14, 0, 26, 0, 11, 0, 3, 0, 32, 0, 31, 0, 120, 0, 21, 0, 32, 0, 145, 0, 18, 0, 24, 0, 21, 0, 8, 0, 3, 0, 26, 0, 120, 0, 14, 0, 30, 0, 21, 0, 26, 0, 17, 0, 108, 0, 121, 0, 21, 0, 31, 0, 28, 0, 145, 0, 18, 0, 30, 0, 21, 0, 8, 0, 3, 0, 29, 0, 34, 0, 120, 0, 21, 0, 32, 0, 145, 0, 18, 0, 24, 0, 21, 0, 8, 0, 3, 0, 25, 0, 32, 0, 31, 0, 34, 0, 120, 0, 14, 0, 26, 0, 18, 0, 8, 0, 3, 0, 32, 0, 31, 0, 145, 0, 120, 0, 21, 0, 31, 0, 28, 0, 145, 0, 18, 0, 30, 0, 21, 0, 8, 0, 3, 0, 24, 0, 120, 0, 33, 0, 30, 0, 17, 0, 108, 0, 21, 0, 8, 0, 3, 0, 120, 0, 21, 0, 21, 0, 31, 0, 28, 0, 145, 0, 121, 0, 18, 0, 30, 0, 21, 0, 10, 0, 2]}
{"text": "ცენტრი წრისა, რომელსაც ცისარტყელა შემოწერს, ძევს წრფეზე, რომელიც გადის დამკვირვებელსა და მზეს შორის, ამავდროულად ცისარტყელას დანახვისას მზე ყოველთვის მდებარეობს დამკვირვებლის ზურგს უკან, შესაბამისად, სპეციალური ოპტიკური ხელსაწყოების გარეშე შეუძლებელია ერთდროულად ცისარტყელასა და მზის დანახვა.", "phonemes": ["t", "s", "ʰ", "ˈ", "e", "n", "t", "r", "i", " ", "t", "s", "r", "ˈ", "i", "s", "a", ",", " ", "r", "ˈ", "o", "m", "e", "l", "s", "a", "t", "s", "ʰ", " ", "t", "s", "ʰ", "ˈ", "i", "s", "a", "r", "t", "q", "ˌ", "e", "l", "a", " ", "ʃ", "ˈ", "e", "m", "o", "t", "s", "e", "r", "s", ",", " ", "d", "z", "ˈ", "e", "v", "s", " ", "t", "s", "r", "p", "ʰ", "ˈ", "e", "z", "e", ",", " ", "r", "ˈ", "o", "m", "e", "l", "i", "t", "s", "ʰ", " ", "ɡ", "ˈ", "a", "d", "i", "s", " ", "d", "ˈ", "a", "m", "k", "v", "i", "r", "v", "ˌ", "e", "b", "e", "l", "s", "a", " ", "d", "a", " ", "m", "z", "ˈ", "e", "s", " ", "ʃ", "ˈ", "o", "r", "i", "s", ",", " ", "ˈ", "a", "m", "a", "v", "d", "r", "ˌ", "o", "u", "l", "a", "d", " ", "t", "s", "ʰ", "ˈ", "i", "s", "a", "r", "t", "q", "ˌ", "e", "l", "a", "s", " ", "d", "ˈ", "a", "n", "a", "x", "v", "ˌ", "i", "s", "a", "s", " ", "m", "z", "ˈ", "e", " ", "q", "ˈ", "o", "v", "e", "l", "t", "ʰ", "v", "i", "s", " ", "m", "d", "ˈ", "e", "b", "a", "r", "ˌ", "e", "o", "b", "s", " ", "d", "ˈ", "a", "m", "k", "v", "i", "r", "v", "ˌ", "e", "b", "l", "i", "s", " ", "z", "ˈ", "u", "r", "ɡ", "s", " ", "ˈ", "u", "k", "a", "n", ",", " ", "ʃ", "ˈ", "e", "s", "a", "b", "ˌ", "a", "m", "i", "s", "a", "d", ",", " ", "s", "p", "ˈ", "e", "t", "s", "ʰ", "i", "ˌ", "a", "l", "u", "r", "i", " ", "ˈ", "o", "p", "t", "i", "k", "ˌ", "u", "r", "i", " ", "x", "ˈ", "e", "l", "s", "a", "t", "s", "q", "ˌ", "o", "e", "b", "i", "s", " ", "ɡ", "ˈ", "a", "r", "e", "ʃ", "e", " ", "ʃ", "ˈ", "e", "u", "d", "z", "l", "ˌ", "e", "b", "e", "l", "ˌ", "i", "a", " ", "ˈ", "e", "r", "t", "ʰ", "d", "r", "o", "ˌ", "u", "l", "a", "d", " ", "t", "s", "ʰ", "ˈ", "i", "s", "a", "r", "t", "q", "ˌ", "e", "l", "a", "s", "a", " ", "d", "a", " ", "m", "z", "ˈ", "i", "s", " ", "d", "ˈ", "a", "n", "a", "x", "v", "a", "."], "phoneme_ids": [1, 0, 32, 0, 31, 0, 145, 0, 120, 0, 18, 0, 26, 0, 32, 0, 30, 0, 21, 0, 3, 0, 32, 0, 31, 0, 30, 0, 120, 0, 21, 0, 31, 0, 14, 0, 8, 0, 3, 0, 30, 0, 120, 0, 27, 0, 25, 0, 18, 0, 24, 0, 31, 0, 14, 0, 32, 0, 31, 0, 145, 0, 3, 0, 32, 0, 31, 0, 145, 0, 120, 0, 21, 0, 31, 0, 14, 0, 30, 0, 32, 0, 29, 0, 121, 0, 18, 0, 24, 0, 14, 0, 3, 0, 96, 0, 120, 0, 18, 0, 25, 0, 27, 0, 32, 0, 31, 0, 18, 0, 30, 0, 31, 0, 8, 0, 3, 0, 17, 0, 38, 0, 120, 0, 18, 0, 34, 0, 31, 0, 3, 0, 32, 0, 31, 0, 30, 0, 28, 0, 145, 0, 120, 0, 18, 0, 38, 0, 18, 0, 8, 0, 3, 0, 30, 0, 120, 0, 27, 0, 25, 0, 18, 0, 24, 0, 21, 0, 32, 0, 31, 0, 145, 0, 3, 0, 66, 0, 120, 0, 14, 0, 17, 0, 21, 0, 31, 0, 3, 0, 17, 0, 120, 0, 14, 0, 25, 0, 23, 0, 34, 0, 21, 0, 30, 0, 34, 0, 121, 0, 18, 0, 15, 0, 18, 0, 24, 0, 31, 0, 14, 0, 3, 0, 17, 0, 14, 0, 3, 0, 25, 0, 38, 0, 120, 0, 18, 0, 31, 0, 3, 0, 96, 0, 120, 0, 27, 0, 30, 0, 21, 0, 31, 0, 8, 0, 3, 0, 120, 0, 14, 0, 25, 0, 14, 0, 34, 0, 17, 0, 30, 0, 121, 0, 27, 0, 33, 0, 24, 0, 14, 0, 17, 0, 3, 0, 32, 0, 31, 0, 145, 0, 120, 0, 21, 0, 31, 0, 14, 0, 30, 0, 32, 0, 29, 0, 121, 0, 18, 0, 24, 0, 14, 0, 31, 0, 3, 0, 17, 0, 120, 0, 14, 0, 26, 0, 14, 0, 36, 0, 34, 0, 121, 0, 21, 0, 31, 0, 14, 0, 31, 0, 3, 0, 25, 0, 38, 0, 120, 0, 18, 0, 3, 0, 29, 0, 120, 0, 27, 0, 34, 0, 18, 0, 24, 0, 32, 0, 145, 0, 34, 0, 21, 0, 31, 0, 3, 0, 25, 0, 17, 0, 120, 0, 18, 0, 15, 0, 14, 0, 30, 0, 121, 0, 18, 0, 27, 0, 15, 0, 31, 0, 3, 0, 17, 0, 120, 0, 14, 0, 25, 0, 23, 0, 34, 0, 21, 0, 30, 0, 34, 0, 121, 0, 18, 0, 15, 0, 24, 0, 21, 0, 31, 0, 3, 0, 38, 0, 120, 0, 33, 0, 30, 0, 66, 0, 31, 0, 3, 0, 120, 0, 33, 0, 23, 0, 14, 0, 26, 0, 8, 0, 3, 0, 96, 0, 120, 0, 18, 0, 31, 0, 14, 0, 15, 0, 121, 0, 14, 0, 25, 0, 21, 0, 31, 0, 14, 0, 17, 0, 8, 0, 3, 0, 31, 0, 28, 0, 120, 0, 18, 0, 32, 0, 31, 0, 145, 0, 21, 0, 121, 0, 14, 0, 24, 0, 33, 0, 30, 0, 21, 0, 3, 0, 120, 0, 27, 0, 28, 0, 32, 0, 21, 0, 23, 0, 121, 0, 33, 0, 30, 0, 21, 0, 3, 0, 36, 0, 120, 0, 18, 0, 24, 0, 31, 0, 14, 0, 32, 0, 31, 0, 29, 0, 121, 0, 27, 0, 18, 0, 15, 0, 21, 0, 31, 0, 3, 0, 66, 0, 120, 0, 14, 0, 30, 0, 18, 0, 96, 0, 18, 0, 3, 0, 96, 0, 120, 0, 18, 0, 33, 0, 17, 0, 38, 0, 24, 0, 121, 0, 18, 0, 15, 0, 18, 0, 24, 0, 121, 0, 21, 0, 14, 0, 3, 0, 120, 0, 18, 0, 30, 0, 32, 0, 145, 0, 17, 0, 30, 0, 27, 0, 121, 0, 33, 0, 24, 0, 14, 0, 17, 0, 3, 0, 32, 0, 31, 0, 145, 0, 120, 0, 21, 0, 31, 0, 14, 0, 30, 0, 32, 0, 29, 0, 121, 0, 18, 0, 24, 0, 14, 0, 31, 0, 14, 0, 3, 0, 17, 0, 14, 0, 3, 0, 25, 0, 38, 0, 120, 0, 21, 0, 31, 0, 3, 0, 17, 0, 120, 0, 14, 0, 26, 0, 14, 0, 36, 0, 34, 0, 14, 0, 10, 0, 2]}
{"text": "ხმელეთზე მდებარე დამკვირვებლისთვის ცისარტყელას, როგორც წესი, აქვს რკალის, წრის ნაწილის, ფორმა.", "phonemes": ["x", "m", "ˈ", "e", "l", "e", "t", "ʰ", "z", "e", " ", "m", "d", "ˈ", "e", "b", "a", "r", "e", " ", "d", "ˈ", "a", "m", "k", "v", "i", "r", "v", "ˌ", "e", "b", "l", "i", "s", "t", "ʰ", "v", "i", "s", " ", "t", "s", "ʰ", "ˈ", "i", "s", "a", "r", "t", "q", "ˌ", "e", "l", "a", "s", ",", " ", "r", "ˈ", "o", "ɡ", "o", "r", "t", "s", "ʰ", " ", "t", "s", "ˈ", "e", "s", "i", ",", " ", "ˈ", "a", "k", "ʰ", "v", "s", " ", "r", "k", "ˈ", "a", "l", "i", "s", ",", " ", "t", "s", "r", "ˈ", "i", "s", " ", "n", "ˈ", "a", "t", "s", "i", "l", "i", "s", ",", " ", "p", "ʰ", "ˈ", "o", "r", "m", "a", "."], "phoneme_ids": [1, 0, 36, 0, 25, 0, 120, 0, 18, 0, 24, 0, 18, 0, 32, 0, 145, 0, 38, 0, 18, 0, 3, 0, 25, 0, 17, 0, 120, 0, 18, 0, 15, 0, 14, 0, 30, 0, 18, 0, 3, 0, 17, 0, 120, 0, 14, 0, 25, 0, 23, 0, 34, 0, 21, 0, 30, 0, 34, 0, 121, 0, 18, 0, 15, 0, 24, 0, 21, 0, 31, 0, 32, 0, 145, 0, 34, 0, 21, 0, 31, 0, 3, 0, 32, 0, 31, 0, 145, 0, 120, 0, 21, 0, 31, 0, 14, 0, 30, 0, 32, 0, 29, 0, 121, 0, 18, 0, 24, 0, 14, 0, 31, 0, 8, 0, 3, 0, 30, 0, 120, 0, 27, 0, 66, 0, 27, 0, 30, 0, 32, 0, 31, 0, 145, 0, 3, 0, 32, 0, 31, 0, 120, 0, 18, 0, 31, 0, 21, 0, 8, 0, 3, 0, 120, 0, 14, 0, 23, 0, 145, 0, 34, 0, 31, 0, 3, 0, 30, 0, 23, 0, 120, 0, 14, 0, 24, 0, 21, 0, 31, 0, 8, 0, 3, 0, 32, 0, 31, 0, 30, 0, 120, 0, 21, 0, 31, 0, 3, 0, 26, 0, 120, 0, 14, 0, 32, 0, 31, 0, 21, 0, 24, 0, 21, 0, 31, 0, 8, 0, 3, 0, 28, 0, 145, 0, 120, 0, 27, 0, 30, 0, 25, 0, 14, 0, 10, 0, 2]}
{"text": "რაც უფრო მაღალია დაკვირვების წერტილი — მით უფრო სრულია ეს რკალი (მთიდან ან თვითმფრინავიდან შესაძლებელია მთლიანი წრის დანახვაც).", "phonemes": ["r", "ˈ", "a", "t", "s", "ʰ", " ", "ˈ", "u", "p", "ʰ", "r", "o", " ", "m", "ˈ", "a", "ɣ", "a", "l", "ˌ", "i", "a", " ", "d", "ˈ", "a", "k", "v", "i", "r", "v", "ˌ", "e", "b", "i", "s", " ", "t", "s", "ˈ", "e", "r", "t", "i", "l", "i", ".", " ", "m", "ˈ", "i", "t", "ʰ", " ", "ˈ", "u", "p", "ʰ", "r", "o", " ", "s", "r", "ˈ", "u", "l", "i", "a", " ", "ˈ", "e", "s", " ", "r", "k", "ˈ", "a", "l", "i", " ", "m", "t", "ʰ", "ˈ", "i", "d", "a", "n", " ", "ˈ", "a", "n", " ", "t", "ʰ", "v", "ˈ", "i", "t", "ʰ", "m", "p", "ʰ", "r", "i", "n", "ˌ", "a", "v", "i", "d", "a", "n", " ", "ʃ", "ˈ", "e", "s", "a", "d", "z", "l", "ˌ", "e", "b", "e", "l", "ˌ", "i", "a", " ", "m", "t", "ʰ", "l", "ˈ", "i", "a", "n", "i", " ", "t", "s", "r", "ˈ", "i", "s", " ", "d", "ˈ", "a", "n", "a", "x", "v", "a", "t", "s", "ʰ"], "phoneme_ids": [1, 0, 30, 0, 120, 0, 14, 0, 32, 0, 31, 0, 145, 0, 3, 0, 120, 0, 33, 0, 28, 0, 145, 0, 30, 0, 27, 0, 3, 0, 25, 0, 120, 0, 14, 0, 68, 0, 14, 0, 24, 0, 121, 0, 21, 0, 14, 0, 3, 0, 17, 0, 120, 0, 14, 0, 23, 0, 34, 0, 21, 0, 30, 0, 34, 0, 121, 0, 18, 0, 15, 0, 21, 0, 31, 0, 3, 0, 32, 0, 31, 0, 120, 0, 18, 0, 30, 0, 32, 0, 21, 0, 24, 0, 21, 0, 10, 0, 3, 0, 25, 0, 120, 0, 21, 0, 32, 0, 145, 0, 3, 0, 120, 0, 33, 0, 28, 0, 145, 0, 30, 0, 27, 0, 3, 0, 31, 0, 30, 0, 120, 0, 33, 0, 24, 0, 21, 0, 14, 0, 3, 0, 120, 0, 18, 0, 31, 0, 3, 0, 30, 0, 23, 0, 120, 0, 14, 0, 24, 0, 21, 0, 3, 0, 25, 0, 32, 0, 145, 0, 120, 0, 21, 0, 17, 0, 14, 0, 26, 0, 3, 0, 120, 0, 14, 0, 26, 0, 3, 0, 32, 0, 145, 0, 34, 0, 120, 0, 21, 0, 32, 0, 145, 0, 25, 0, 28, 0, 145, 0, 30, 0, 21, 0, 26, 0, 121, 0, 14, 0, 34, 0, 21, 0, 17, 0, 14, 0, 26, 0, 3, 0, 96, 0, 120, 0, 18, 0, 31, 0, 14, 0, 17, 0, 38, 0, 24, 0, 121, 0, 18, 0, 15, 0, 18, 0, 24, 0, 121, 0, 21, 0, 14, 0, 3, 0, 25, 0, 32, 0, 145, 0, 24, 0, 120, 0, 21, 0, 14, 0, 26, 0, 21, 0, 3, 0, 32, 0, 31, 0, 30, 0, 120, 0, 21, 0, 31, 0, 3, 0, 17, 0, 120, 0, 14, 0, 26, 0, 14, 0, 36, 0, 34, 0, 14, 0, 32, 0, 31, 0, 145, 0, 2]}
{"text": "როდესაც მზე აღიმართება ჰორიზონტიდან 42 გრადუსზე უფრო მაღლა, ცისარტყელა დედამიწის ზედაპირიდან უხილავია.", "phonemes": ["r", "ˈ", "o", "d", "e", "s", "a", "t", "s", "ʰ", " ", "m", "z", "ˈ", "e", " ", "ˈ", "a", "ɣ", "i", "m", "ˌ", "a", "r", "t", "ʰ", "e", "b", "a", " ", "h", "ˈ", "o", "r", "i", "z", "ˌ", "o", "n", "t", "i", "d", "a", "n", " ", "ˈ", "o", "r", "m", "o", "t", "s", "d", "a", "ˈ", "o", "r", "i", " ", "ɡ", "r", "ˈ", "a", "d", "u", "s", "z", "e", " ", "ˈ", "u", "p", "ʰ", "r", "o", " ", "m", "ˈ", "a", "ɣ", "l", "a", ",", " ", "t", "s", "ʰ", "ˈ", "i", "s", "a", "r", "t", "q", "ˌ", "e", "l", "a", " ", "d", "ˈ", "e", "d", "a", "m", "ˌ", "i", "t", "s", "i", "s", " ", "z", "ˈ", "e", "d", "a", "p", "ˌ", "i", "r", "i", "d", "a", "n", " ", "ˈ", "u", "x", "i", "l", "ˌ", "a", "v", "i", "a", "."], "phoneme_ids": [1, 0, 30, 0, 120, 0, 27, 0, 17, 0, 18, 0, 31, 0, 14, 0, 32, 0, 31, 0, 145, 0, 3, 0, 25, 0, 38, 0, 120, 0, 18, 0, 3, 0, 120, 0, 14, 0, 68, 0, 21, 0, 25, 0, 121, 0, 14, 0, 30, 0, 32, 0, 145, 0, 18, 0, 15, 0, 14, 0, 3, 0, 20, 0, 120, 0, 27, 0, 30, 0, 21, 0, 38, 0, 121, 0, 27, 0, 26, 0, 32, 0, 21, 0, 17, 0, 14, 0, 26, 0, 3, 0, 120, 0, 27, 0, 30, 0, 25, 0, 27, 0, 32, 0, 31, 0, 17, 0, 14, 0, 120, 0, 27, 0, 30, 0, 21, 0, 3, 0, 66, 0, 30, 0, 120, 0, 14, 0, 17, 0, 33, 0, 31, 0, 38, 0, 18, 0, 3, 0, 120, 0, 33, 0, 28, 0, 145, 0, 30, 0, 27, 0, 3, 0, 25, 0, 120, 0, 14, 0, 68, 0, 24, 0, 14, 0, 8, 0, 3, 0, 32, 0, 31, 0, 145, 0, 120, 0, 21, 0, 31, 0, 14, 0, 30, 0, 32, 0, 29, 0, 121, 0, 18, 0, 24, 0, 14, 0, 3, 0, 17, 0, 120, 0, 18, 0, 17, 0, 14, 0, 25, 0, 121, 0, 21, 0, 32, 0, 31, 0, 21, 0, 31, 0, 3, 0, 38, 0, 120, 0, 18, 0, 17, 0, 14, 0, 28, 0, 121, 0, 21, 0, 30, 0, 21, 0, 17, 0, 14, 0, 26, 0, 3, 0, 120, 0, 33, 0, 36, 0, 21, 0, 24, 0, 121, 0, 14, 0, 34, 0, 21, 0, 14, 0, 10, 0, 2]}
