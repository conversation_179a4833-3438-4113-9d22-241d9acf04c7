FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Install uv.
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Copy the application into the container.
COPY . /app

# Install the application dependencies.
WORKDIR /app

# Create virtual environment and install requirements using uv
RUN uv venv --python 3.10
RUN uv pip install -r requirements.txt
RUN uv pip install -r requirements_http.txt

# Expose port for the HTTP server
EXPOSE 5000

# Run the Piper TTS HTTP server using the virtual environment
CMD ["/app/.venv/bin/python", "-m", "src.python_run.piper.http_server"]