version: '3.8'

services:
  piper-tts:
    build:
      context: .
      dockerfile: Dockerfile.tts
    ports:
      - "8300:5000"
    volumes:
      - ./models:/app/models
      - ./config:/app/config
    environment:
      - PYTHONPATH=/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s